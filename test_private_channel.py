import asyncio
import os
import re
from dotenv import load_dotenv
from pyrogram import Client

# تحميل متغيرات البيئة
load_dotenv()

API_ID = int(os.getenv("API_ID"))
API_HASH = os.getenv("API_HASH")
BOT_TOKEN = os.getenv("BOT_TOKEN")

# إنشاء العميل
app = Client("test_bot", api_id=API_ID, api_hash=API_HASH, bot_token=BOT_TOKEN)

async def test_private_channel():
    await app.start()
    
    # الرابط المراد اختباره
    url = "https://t.me/c/2349294062/526"
    
    print(f"🔍 اختبار الرابط: {url}")
    
    # استخراج معرف القناة ورقم المنشور
    match = re.match(r'https?://t\.me/c/(-?\d+)/(\d+)', url)
    if match:
        channel_id_raw = match.group(1)
        message_id = int(match.group(2))

        print(f"📊 معرف القناة الخام: {channel_id_raw}")

        # تجربة طرق مختلفة لتحويل معرف القناة
        channel_ids_to_try = []

        # الطريقة الأولى: إضافة -100
        if not channel_id_raw.startswith('-'):
            channel_ids_to_try.append(int(f"-100{channel_id_raw}"))

        # الطريقة الثانية: استخدام المعرف كما هو
        channel_ids_to_try.append(int(channel_id_raw))

        # الطريقة الثالثة: إزالة - وإضافة -100
        if channel_id_raw.startswith('-'):
            clean_id = channel_id_raw[1:]
            channel_ids_to_try.append(int(f"-100{clean_id}"))

        print(f"📝 رقم المنشور: {message_id}")
        print(f"🔄 سأجرب هذه المعرفات: {channel_ids_to_try}")

        # تجربة كل معرف
        for i, channel_id in enumerate(channel_ids_to_try):
            print(f"\n🔍 تجربة رقم {i+1}: {channel_id}")
        
        try:
            # محاولة الحصول على معلومات القناة
            print("🔍 محاولة الحصول على معلومات القناة...")
            chat = await app.get_chat(channel_id)
            print(f"✅ اسم القناة: {chat.title}")
            print(f"📋 نوع القناة: {chat.type}")
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات القناة: {e}")
        
        try:
            # محاولة التحقق من صلاحيات البوت
            print("🔍 محاولة التحقق من صلاحيات البوت...")
            bot_member = await app.get_chat_member(channel_id, "me")
            print(f"✅ حالة البوت: {bot_member.status}")
            
            if hasattr(bot_member, 'privileges') and bot_member.privileges:
                print(f"🔧 صلاحيات البوت:")
                print(f"   - can_manage_chat: {getattr(bot_member.privileges, 'can_manage_chat', False)}")
                print(f"   - can_delete_messages: {getattr(bot_member.privileges, 'can_delete_messages', False)}")
                print(f"   - can_edit_messages: {getattr(bot_member.privileges, 'can_edit_messages', False)}")
            
        except Exception as e:
            print(f"❌ خطأ في التحقق من صلاحيات البوت: {e}")
        
        try:
            # محاولة سحب المنشور
            print("🔍 محاولة سحب المنشور...")
            msg = await app.get_messages(channel_id, message_id)
            
            if msg and not msg.empty:
                print(f"✅ تم العثور على المنشور!")
                print(f"📝 نوع المحتوى: {msg.media if msg.media else 'نص'}")
                print(f"📄 النص: {msg.text[:100] if msg.text else 'لا يوجد نص'}...")
                print("✅ السحب ممكن!")
            else:
                print("❌ المنشور فارغ أو غير موجود")
                
        except Exception as e:
            print(f"❌ خطأ في سحب المنشور: {e}")
    
    else:
        print("❌ تنسيق الرابط غير صحيح")
    
    await app.stop()

if __name__ == "__main__":
    asyncio.run(test_private_channel())
