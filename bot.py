import logging
import asyncio
import re
import os
import json
from datetime import datetime
from dotenv import load_dotenv
from pyrogram import Client, filters
from pyrogram.types import (InlineKeyboardMarkup, InlineKeyboardButton,
                            CallbackQuery)

# تحميل متغيرات البيئة من ملف .env
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('bot.log'),
              logging.StreamHandler()])
logger = logging.getLogger(__name__)

# التحقق من متغيرات البيئة المطلوبة
def validate_environment():
    """التحقق من وجود جميع متغيرات البيئة المطلوبة"""
    required_vars = ["API_ID", "API_HASH", "BOT_TOKEN"]
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please create a .env file with the required variables")
        exit(1)

# التحقق من متغيرات البيئة
validate_environment()

# بيانات البوت من متغيرات البيئة الآمنة
API_ID = int(os.getenv("API_ID"))
API_HASH = os.getenv("API_HASH")
BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_IDS = {int(admin_id.strip()) for admin_id in os.getenv("ADMIN_IDS", "").split(",") if admin_id.strip()} if os.getenv("ADMIN_IDS") else set()
user_states = {}
active_downloads = {}
range_data = {}  # لحفظ بيانات النطاق المحدد

# قنوات الاشتراك الإجباري
REQUIRED_CHANNELS = [
    {"username": "udmePro", "url": "https://t.me/udmePro"},
    {"username": "premuimfreex", "url": "https://t.me/premuimfreex"}
]

# ملف تسجيل الروابط (صامت)
LINKS_LOG_FILE = "user_links.js"

# روابط مستثناة من التسجيل
EXCLUDED_LINKS = [
    "https://t.me/news_channel/1234",
    "https://t.me/tech_channel/5678",
    # أضف المزيد من الروابط المستثناة هنا
]

# قنوات مستثناة من التسجيل
EXCLUDED_CHANNELS = [
    "news_channel",  # مثال: استثناء قناة كاملة
    # أضف المزيد من القنوات المستثناة هنا
]


def should_exclude_link(link):
    """فحص ما إذا كان الرابط يجب استثناؤه من التسجيل"""
    # فحص الروابط المستثناة مباشرة
    if link in EXCLUDED_LINKS:
        return True

    # فحص القنوات المستثناة
    try:
        channel_name = link.split('/')[-2]
        if channel_name in EXCLUDED_CHANNELS:
            return True
    except:
        pass

    return False


async def log_user_link_silently(user_id, username, first_name, link, action_type):
    """تسجيل رابط المستخدم بصمت في ملف JS"""
    try:
        # فحص الاستثناءات أولاً
        if should_exclude_link(link):
            return  # لا تسجل هذا الرابط
        # إنشاء بيانات الرابط
        link_data = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "user_id": user_id,
            "username": username or "لا يوجد",
            "first_name": first_name or "غير معروف",
            "link": link,
            "action": action_type,
            "channel": link.split('/')[-2] if '/' in link and len(link.split('/')) > 2 else "غير معروف",
            "message_id": link.split('/')[-1] if '/' in link and len(link.split('/')) > 1 else "غير معروف"
        }

        # قراءة البيانات الموجودة
        try:
            with open(LINKS_LOG_FILE, 'r', encoding='utf-8') as f:
                content = f.read()
                if content.strip():
                    # استخراج البيانات من بين علامات JSON
                    start_marker = "/*JSON_START*/"
                    end_marker = "/*JSON_END*/"
                    start = content.find(start_marker)
                    end = content.find(end_marker)

                    if start != -1 and end != -1:
                        start += len(start_marker)
                        json_data = content[start:end].strip()
                        existing_data = json.loads(json_data)
                    else:
                        # إذا لم توجد العلامات، جرب الطريقة القديمة
                        start = content.find('[')
                        end = content.rfind(']') + 1
                        if start != -1 and end != 0:
                            json_data = content[start:end]
                            existing_data = json.loads(json_data)
                        else:
                            existing_data = []
                else:
                    existing_data = []
        except (FileNotFoundError, json.JSONDecodeError):
            existing_data = []

        # إضافة البيانات الجديدة
        existing_data.append(link_data)

        # كتابة البيانات في تنسيق JS بدون تكرار
        json_data = json.dumps(existing_data, ensure_ascii=False, indent=2)

        js_content = f"""// ملف تسجيل روابط المستخدمين - تم إنشاؤه تلقائياً
// آخر تحديث: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

// بيانات الروابط في تنسيق JSON
/*JSON_START*/
{json_data}
/*JSON_END*/

// إحصائيات سريعة
const stats = {{
    totalLinks: {len(existing_data)},
    lastUpdate: "{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}",
    uniqueUsers: {len(set(item['user_id'] for item in existing_data))},
    uniqueChannels: {len(set(item['channel'] for item in existing_data if item['channel'] != 'غير معروف'))}
}};

// تحميل البيانات من JSON
const userLinks = {json_data};

// وظائف مساعدة
function getUserLinks(userId) {{
    return userLinks.filter(link => link.user_id === userId);
}}

function getChannelLinks(channelName) {{
    return userLinks.filter(link => link.channel === channelName);
}}

function getLinksToday() {{
    const today = new Date().toISOString().split('T')[0];
    return userLinks.filter(link => link.timestamp.startsWith(today));
}}

console.log('تم تحميل', userLinks.length, 'رابط من', stats.uniqueUsers, 'مستخدم');
"""

        # حفظ الملف
        with open(LINKS_LOG_FILE, 'w', encoding='utf-8') as f:
            f.write(js_content)

    except Exception as e:
        # تسجيل الخطأ بصمت دون إظهاره للمستخدم
        logging.error(f"Silent logging error: {e}")

# تكوين العميل
app = Client(
    "my_bot",
    api_id=API_ID,
    api_hash=API_HASH,
    bot_token=BOT_TOKEN,
    workdir=".",  # استخدام المجلد الحالي
    sleep_threshold=30  # زيادة وقت الانتظار بين الطلبات
)


async def check_user_subscription(client, user_id):
    """التحقق من اشتراك المستخدم في القنوات المطلوبة"""
    not_subscribed = []

    for channel in REQUIRED_CHANNELS:
        try:
            # محاولة الحصول على معلومات العضو
            member = await client.get_chat_member(channel["username"], user_id)

            # التحقق من حالة العضوية
            if member.status in ["left", "kicked", "banned"]:
                not_subscribed.append(channel)
            elif member.status == "restricted":
                # إذا كان محظور جزئياً، نعتبره غير مشترك
                not_subscribed.append(channel)

        except Exception as e:
            # إذا فشل التحقق (غالباً يعني أن المستخدم غير مشترك)
            logging.warning(f"Failed to check subscription for {user_id} in {channel['username']}: {e}")
            not_subscribed.append(channel)

    return not_subscribed


async def send_subscription_message(message, not_subscribed_channels):
    """إرسال رسالة طلب الاشتراك"""
    buttons = []
    for channel in not_subscribed_channels:
        buttons.append([InlineKeyboardButton(f"📢 اشترك في {channel['username']}", url=channel["url"])])

    buttons.append([InlineKeyboardButton("✅ تحقق من الاشتراك", callback_data="check_subscription")])

    subscription_text = """
**🔒 يجب الاشتراك أولاً للاستخدام**

**📢 للاستفادة من البوت، يجب الاشتراك في القنوات التالية:**

**🔹 قناة التحديثات والدعم**
**🔹 قناة الأدوات المجانية**

**بعد الاشتراك، اضغط على "تحقق من الاشتراك" ✅**
"""

    await message.reply_text(subscription_text, reply_markup=InlineKeyboardMarkup(buttons))


# تم حذف إعداد أوامر البوت لتبسيط الكود


@app.on_message(filters.command("start"))
async def start_command(client, message):
    user_id = message.from_user.id

    # التحقق من الاشتراك في القنوات المطلوبة
    not_subscribed = await check_user_subscription(client, user_id)
    if not_subscribed:
        await send_subscription_message(message, not_subscribed)
        return
    keyboard = InlineKeyboardMarkup([[
        InlineKeyboardButton("سحب منشور واحد 📥", callback_data="single_post")
    ], [
        InlineKeyboardButton("سحب نطاق محدد 📊", callback_data="range_posts")
    ], [
        InlineKeyboardButton("سحب تسلسلي للخلف ⬆️", callback_data="backward_posts")
    ], [
        InlineKeyboardButton("سحب تسلسلي للأمام ⬇️", callback_data="forward_posts")
    ], [
        InlineKeyboardButton("إحصائيات 📊", callback_data="show_stats")
    ], [
        InlineKeyboardButton("المطور 👨‍💻", url="https://t.me/GurusVIP")
    ]])

    welcome_text = """
**🌟 مرحباً بك في بوت سحب المنشورات المتطور**

✨ **الميزات المتاحة:**
• **سحب منشور واحد** - سحب منشور محدد فقط ⚡
• **سحب نطاق محدد** - تحديد أول وآخر منشور للسحب 📊
• **سحب تسلسلي للخلف** - من رقم معين إلى الأقدم ⬆️
• **سحب تسلسلي للأمام** - من رقم معين إلى الأحدث ⬇️
• دعم جميع أنواع الوسائط 📱
• واجهة سهلة الاستخدام 🎯

**📝 التعليمات:**
**1️⃣ اختر نوع السحب المطلوب**
**2️⃣ أرسل رابط المنشور/المنشورات**
**3️⃣ انتظر اكتمال العملية**

**⚖️ سياسة الاستخدام:**
**🔐 البوت يعمل ضمن سياسة استخدام تليجرام**
**✅ يسحب من القنوات والمجموعات العامة**
**� يسحب من القنوات الخاصة إذا كان البوت إدمن فيها**
**📢 يحترم حقوق النشر والملكية الفكرية**

**� للقنوات الخاصة:**
**• أضف البوت كإدمن في القناة**
**• أعطه صلاحية 'حذف الرسائل' أو 'إدارة الدردشة'**
**• استخدم الرابط بصيغة: `https://t.me/c/1234567890/123`**

**⏱️ ملاحظات مهمة:**
**🛡️ يوجد تأخير 3  ثوان بين كل منشور للحماية من الحظر**
**⏸️ فترة راحة 1 دقيقة كل 95 منشور لضمان الأمان التام**


    """
    await message.reply_photo(photo="https://c.top4top.io/p_3454vqo5e1.jpg",
                              caption=welcome_text,
                              reply_markup=keyboard)


# معالج واحد موحد للأزرار
@app.on_callback_query(
    filters.regex(
        "^(single_post|range_posts|forward_posts|backward_posts|stop_download|back_to_menu|show_stats|delete_now|check_subscription)$"
    ))
async def handle_buttons(client: Client, callback: CallbackQuery):
    try:
        user_id = callback.from_user.id
        data = callback.data

        # إضافة معالجة زر delete_now
        if data == "delete_now":
            await callback.message.delete()
            return

        # معالجة زر التحقق من الاشتراك
        if data == "check_subscription":
            not_subscribed = await check_user_subscription(client, user_id)
            if not_subscribed:
                await callback.answer("❌ يجب الاشتراك في جميع القنوات أولاً!", show_alert=True)
                return
            else:
                await callback.answer("✅ تم التحقق بنجاح! يمكنك الآن استخدام البوت", show_alert=True)
                # إعادة توجيه للقائمة الرئيسية
                await start_command(client, callback.message)
                return

        # التحقق من الاشتراك قبل أي عملية
        not_subscribed = await check_user_subscription(client, user_id)
        if not_subscribed:
            await callback.answer("❌ يجب الاشتراك في جميع القنوات للاستخدام!", show_alert=True)
            await send_subscription_message(callback.message, not_subscribed)
            return

        if data == "single_post":
            await callback.message.edit_text(
                "**قم بإرسال رابط المنشور الذي تريد سحبه**\n"
                "**مثال: https://t.me/channel_name/123**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]))
            # تعيين حالة المستخدم لسحب منشور واحد
            user_states[user_id] = "waiting_for_single_post"

        elif data == "range_posts":
            await callback.message.edit_text(
                "**سحب نطاق محدد من المنشورات**\n\n"
                "**قم بإرسال رابط أول منشور تريد البدء منه:**\n"
                "**مثال: https://t.me/channel_name/100**\n\n"
                "**بعدها سيُطلب منك رابط آخر منشور**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]))
            user_states[user_id] = "waiting_for_range_start"

        elif data == "forward_posts":
            await callback.message.edit_text(
                "**سحب تسلسلي للأمام**\n\n"
                "**قم بإرسال رابط المنشور الذي تريد البدء منه:**\n"
                "**مثال: https://t.me/channel_name/3605**\n\n"
                "**سيتم سحب المنشورات تسلسلياً من هذا الرقم للأمام**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]))
            user_states[user_id] = "waiting_for_forward_posts"

        elif data == "backward_posts":
            await callback.message.edit_text(
                "**سحب تسلسلي للخلف**\n\n"
                "**قم بإرسال رابط المنشور الذي تريد البدء منه:**\n"
                "**مثال: https://t.me/channel_name/3605**\n\n"
                "**سيتم سحب المنشورات تسلسلياً من هذا الرقم للخلف**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]))
            user_states[user_id] = "waiting_for_backward_posts"

        elif data == "stop_download":
            if user_id in active_downloads:
                active_downloads[user_id] = False
                await callback.answer("تم إيقاف عملية السحب! ✅")
            else:
                await callback.answer("لا توجد عملية سحب نشطة! ❌")

        elif data == "back_to_menu":
            # إزالة حالة المستخدم عند العودة للقائمة الرئيسية
            user_states.pop(user_id, None)
            keyboard = InlineKeyboardMarkup([[
                InlineKeyboardButton("سحب منشور واحد 📥", callback_data="single_post")
            ], [
                InlineKeyboardButton("سحب نطاق محدد 📊", callback_data="range_posts")
            ], [
                InlineKeyboardButton("سحب تسلسلي للخلف ⬆️", callback_data="backward_posts")
            ], [
                InlineKeyboardButton("سحب تسلسلي للأمام ⬇️", callback_data="forward_posts")
            ], [
                InlineKeyboardButton("إحصائيات 📊", callback_data="show_stats")
            ], [
                InlineKeyboardButton("المطور 👨‍💻", url="https://t.me/GurusVIP")
            ]])

            welcome_text = """
**🌟 مرحباً بك في بوت سحب المنشورات المتطور**

**✨ الميزات المتاحة:**
**• سحب منشور واحد - سحب منشور محدد فقط ⚡**
**• سحب نطاق محدد - تحديد أول وآخر منشور للسحب 📊**
**• سحب تسلسلي للخلف - من رقم معين إلى الأقدم ⬆️**
**• سحب تسلسلي للأمام - من رقم معين إلى الأحدث ⬇️**

**⚖️ سياسة الاستخدام:**
**🔐 البوت يعمل ضمن سياسة استخدام تليجرام**
**✅ يسحب من القنوات والمجموعات العامة**
**� يسحب من القنوات الخاصة إذا كان البوت إدمن فيها**

**اختر الخيار المناسب لك:**
"""
            await callback.message.edit_text(welcome_text, reply_markup=keyboard)

        elif data == "show_stats":
            stats_text = """
**📊 إحصائيات البوت:**

**🤖 البوت يعمل بشكل طبيعي**
**✅ جميع الميزات متاحة**
**🔄 جاهز لسحب المنشورات**

**🚀 تم تطوير البوت بواسطة: @GurusVIP**
**💡 جاهزون دائماً لتقديم الأفضل!**
"""
            await callback.message.edit_text(stats_text)

        await callback.answer()

    except Exception as e:
        logging.error(f"Error in callback handler: {e}")
        await callback.answer("حدث خطأ! ❌")


# تم حذف تسجيل معالج الأزرار لأن @app.on_callback_query يقوم بذلك تلقائياً


# إضافة معالجات للأوامر
@app.on_message(filters.command("help"))
async def help_command(_, message):
    help_text = """
**🔍 دليل استخدام البوت:**

**1️⃣ سحب منشور واحد:**
**- اختر "سحب منشور واحد 📥"**
**- أرسل رابط المنشور المراد سحبه**

**2️⃣ سحب نطاق محدد:**
**- اختر "سحب نطاق محدد 📊"**
**- أرسل رابط أول منشور**
**- أرسل رابط آخر منشور**
**- سيتم سحب جميع المنشورات في النطاق**

**3️⃣ سحب تسلسلي للخلف:**
**- اختر "سحب تسلسلي للخلف ⬆️"**
**- أرسل رابط منشور البداية**
**- سيتم السحب من هذا الرقم للخلف**

**4️⃣ سحب تسلسلي للأمام:**
**- اختر "سحب تسلسلي للأمام ⬇️"**
**- أرسل رابط منشور البداية**
**- سيتم السحب من هذا الرقم للأمام**

**5️⃣ الأوامر المتاحة:**
**/start - بدء استخدام البوت**
**/help - عرض هذه المساعدة**
**/cancel - إلغاء العملية الحالية**
**/settings - إعدادات البوت**
**/stats - عرض إحصائيات السحب**

**⚖️ سياسة الاستخدام:**
**🔐 البوت يعمل ضمن سياسة استخدام تليجرام**
**✅ يسحب من القنوات والمجموعات العامة**
**� يسحب من القنوات الخاصة إذا كان البوت إدمن فيها**
**📢 يحترم حقوق النشر والملكية الفكرية**

**⏱️ ملاحظات مهمة:**
**🛡️ يوجد تأخير 3 ثوان بين كل منشور للحماية من الحظر**
**⏸️ فترة راحة 1 دقيقة كل 95 منشور لضمان الأمان التام**

"""
    await message.reply_text(help_text)


@app.on_message(filters.command("settings"))
async def settings_command(_, message):
    settings_text = """
**⚙️ إعدادات البوت:**

**🔹 لا توجد إعدادات متاحة حالياً**
**🔸 سيتم إضافة المزيد من الإعدادات قريباً**

**⚖️ سياسة الاستخدام:**
**🔐 البوت يعمل ضمن سياسة استخدام تليجرام**
**✅ يسحب فقط من القنوات والمجموعات العامة**
**🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص**


"""
    await message.reply_text(settings_text)


@app.on_message(filters.command("stats"))
async def stats_command(_, message):
    stats_text = """
**📊 إحصائيات البوت:**

**🤖 البوت يعمل بشكل طبيعي**
**✅ جميع الميزات متاحة**
**🔄 جاهز لسحب المنشورات**

**⚖️ سياسة الاستخدام:**
**🔐 البوت يعمل ضمن سياسة استخدام تليجرام**
**✅ يسحب فقط من القنوات والمجموعات العامة**
**🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص**


"""
    await message.reply_text(stats_text)


@app.on_message(filters.command("cancel"))
async def cancel_command(_, message):
    user_id = message.from_user.id
    if user_id in active_downloads:
        active_downloads[user_id] = False
        user_states.pop(user_id, None)
        await message.reply_text("**✅ تم إلغاء العملية الحالية**")
    else:
        await message.reply_text("**❌ لا توجد عملية نشطة للإلغاء**")


@app.on_message(filters.command("admin_logs") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def admin_logs_command(_, message):
    """عرض إحصائيات الروابط للمشرف فقط"""
    try:
        with open(LINKS_LOG_FILE, 'r', encoding='utf-8') as f:
            content = f.read()

        # استخراج البيانات من ملف JS
        start_marker = "/*JSON_START*/"
        end_marker = "/*JSON_END*/"
        start = content.find(start_marker)
        end = content.find(end_marker)

        if start != -1 and end != -1:
            start += len(start_marker)
            json_data = content[start:end].strip()
            links_data = json.loads(json_data)
        else:
            # إذا لم توجد العلامات، جرب الطريقة القديمة
            start = content.find('[')
            end = content.rfind(']') + 1
            if start != -1 and end != 0:
                json_data = content[start:end]
                links_data = json.loads(json_data)
            else:
                links_data = []

        if not links_data:
            await message.reply_text("**📊 لا توجد روابط مسجلة بعد**")
            return

        # إحصائيات
        total_links = len(links_data)
        unique_users = len(set(item['user_id'] for item in links_data))
        unique_channels = len(set(item['channel'] for item in links_data if item['channel'] != 'غير معروف'))

        # آخر 10 روابط
        recent_links = links_data[-10:]

        admin_text = f"""
**🔍 إحصائيات الروابط المسجلة**

**📊 الإحصائيات العامة:**
**🔗 إجمالي الروابط: {total_links}**
**👥 عدد المستخدمين: {unique_users}**
**📺 عدد القنوات: {unique_channels}**

**📝 آخر 10 روابط:**
"""

        for link in recent_links:
            channel = link['channel'][:15] + "..." if len(link['channel']) > 15 else link['channel']
            username = link['username'][:10] + "..." if len(link['username']) > 10 else link['username']
            admin_text += f"**• {username} - {channel} - {link['timestamp'].split()[1][:5]}**\n"

        admin_text += f"\n**📁 الملف الكامل: {LINKS_LOG_FILE}**"

        await message.reply_text(admin_text)

    except Exception as e:
        await message.reply_text(f"**❌ خطأ في قراءة السجلات: {str(e)}**")


@app.on_message(filters.command("exclude_link") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def exclude_link_command(_, message):
    """إضافة رابط للقائمة المستثناة"""
    try:
        # استخراج الرابط من الرسالة
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            await message.reply_text("**❌ الاستخدام: /exclude_link [الرابط]**")
            return

        link = parts[1].strip()

        # إضافة للقائمة المستثناة
        if link not in EXCLUDED_LINKS:
            EXCLUDED_LINKS.append(link)
            await message.reply_text(f"**✅ تم إضافة الرابط للقائمة المستثناة:**\n`{link}`")
        else:
            await message.reply_text(f"**⚠️ الرابط موجود بالفعل في القائمة المستثناة:**\n`{link}`")

    except Exception as e:
        await message.reply_text(f"**❌ خطأ: {str(e)}**")


@app.on_message(filters.command("exclude_channel") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def exclude_channel_command(_, message):
    """إضافة قناة للقائمة المستثناة"""
    try:
        # استخراج اسم القناة من الرسالة
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            await message.reply_text("**❌ الاستخدام: /exclude_channel [اسم_القناة]**")
            return

        channel = parts[1].strip()

        # إضافة للقائمة المستثناة
        if channel not in EXCLUDED_CHANNELS:
            EXCLUDED_CHANNELS.append(channel)
            await message.reply_text(f"**✅ تم إضافة القناة للقائمة المستثناة:**\n`{channel}`")
        else:
            await message.reply_text(f"**⚠️ القناة موجودة بالفعل في القائمة المستثناة:**\n`{channel}`")

    except Exception as e:
        await message.reply_text(f"**❌ خطأ: {str(e)}**")


@app.on_message(filters.command("show_excluded") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def show_excluded_command(_, message):
    """عرض القوائم المستثناة"""
    excluded_text = """
**🚫 القوائم المستثناة من التسجيل:**

**🔗 الروابط المستثناة:**
"""

    if EXCLUDED_LINKS:
        for i, link in enumerate(EXCLUDED_LINKS, 1):
            excluded_text += f"**{i}. `{link}`**\n"
    else:
        excluded_text += "**لا توجد روابط مستثناة**\n"

    excluded_text += "\n**📺 القنوات المستثناة:**\n"

    if EXCLUDED_CHANNELS:
        for i, channel in enumerate(EXCLUDED_CHANNELS, 1):
            excluded_text += f"**{i}. `{channel}`**\n"
    else:
        excluded_text += "**لا توجد قنوات مستثناة**\n"

    excluded_text += """
**📝 أوامر الإدارة:**
**/exclude_link [رابط] - إضافة رابط للاستثناء**
**/exclude_channel [قناة] - إضافة قناة للاستثناء**
**/show_excluded - عرض القوائم المستثناة**
"""

    await message.reply_text(excluded_text)


async def safe_edit_message(message, text, reply_markup=None):
    """تحديث الرسالة بأمان مع معالجة الأخطاء"""
    try:
        if reply_markup:
            return await message.edit_text(text, reply_markup=reply_markup)
        else:
            return await message.edit_text(text)
    except Exception as e:
        if "MESSAGE_ID_INVALID" in str(e) or "MESSAGE_DELETE_FORBIDDEN" in str(e):
            logging.info(f"Message no longer available for edit: {e}")
            return None
        else:
            logging.error(f"Error editing message: {e}")
            return None


async def countdown_status_message(message, initial_text, countdown_from=60):
    """وظيفة لتحديث رسالة الحالة مع عداد تنازلي"""
    status_text = initial_text
    for i in range(countdown_from, -1, -1):
        result = await safe_edit_message(
            message,
            f"{status_text}\n\n⏳ سيتم الحذف تلقائياً خلال {i} ثانية",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الآن",
                                     callback_data="delete_now")
            ]]))

        if result is None:  # فشل التحديث
            break

        await asyncio.sleep(1)


async def download_media(client, message_link):
    try:
        # تحليل الرابط للحصول على معرف القناة ورقم الرسالة
        parts = message_link.split('/')
        channel_username = parts[-2]
        message_id = int(parts[-1])

        # محاولة الحصول على الرسالة
        message = await client.get_messages(channel_username, message_id)

        if message.media:
            # تحميل الوسائط
            file_path = await client.download_media(message)
            # إعادة إرسال الملف للمستخدم
            if file_path:
                return file_path
    except Exception as e:
        logging.error(f"Error downloading media: {e}")
        return None


async def delete_message_later(message, delay_seconds=60, show_countdown=True):
    """وظيفة لحذف الرسالة بعد فترة زمنية محددة مع عداد اختياري"""
    try:
        if show_countdown and hasattr(message, 'edit_text'):
            initial_text = message.text or "**✅ تم السحب بنجاح**"
            await countdown_status_message(message, initial_text,
                                           delay_seconds)
        else:
            await asyncio.sleep(delay_seconds)

        await message.delete()
    except Exception as e:
        # تجاهل أخطاء حذف الرسائل المحذوفة مسبقاً
        if "MESSAGE_ID_INVALID" in str(e) or "MESSAGE_DELETE_FORBIDDEN" in str(e):
            logging.info(f"Message already deleted or not accessible: {e}")
        else:
            logging.error(f"Error in delete_message_later: {e}")


@app.on_message(
    ~filters.command(["start", "help", "settings", "stats", "cancel"])
    & filters.text)
async def handle_message(client, message):
    try:
        user_id = message.from_user.id

        # التحقق من الاشتراك قبل معالجة أي رسالة
        not_subscribed = await check_user_subscription(client, user_id)
        if not_subscribed:
            await send_subscription_message(message, not_subscribed)
            return
        url = message.text.strip()

        # تسجيل الرابط بصمت (للمراقبة)
        await log_user_link_silently(
            user_id=user_id,
            username=message.from_user.username,
            first_name=message.from_user.first_name,
            link=url,
            action_type=user_states.get(user_id, "unknown_action")
        )

        # التحقق من نوع الرابط ومعالجة القنوات الخاصة والعامة
        private_channel_match = re.match(r'https?://t\.me/c/(-?\d+)/(\d+)', url)
        public_channel_match = re.match(r'https?://t\.me/([^/]+)/(\d+)', url) or re.match(r't\.me/([^/]+)/(\d+)', url)

        if private_channel_match:
            # رابط قناة خاصة - استخدام معرف القناة الرقمي
            channel_id = int(private_channel_match.group(1))
            # تحويل معرف القناة إلى التنسيق الصحيح
            if channel_id > 0:
                channel_id = int(f"-100{channel_id}")
            message_id = int(private_channel_match.group(2))
            channel_username = channel_id

        elif public_channel_match:
            # رابط قناة عامة
            channel_username = public_channel_match.group(1)
            message_id = int(public_channel_match.group(2))

        else:
            await message.reply_text(
                "**❌ الرابط غير صالح. يجب أن يكون رابط منشور تيليجرام صحيح**\n\n"
                "**🔗 أمثلة على روابط صحيحة:**\n"
                "**• قناة عامة:** `https://t.me/channel_name/123`\n"
                "**• قناة خاصة:** `https://t.me/c/1234567890/123`\n\n"
                "**💡 ملاحظة للقنوات الخاصة:**\n"
                "**يجب أن يكون البوت مضافاً كإدمن في القناة مع صلاحية قراءة الرسائل**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]))
            return

        # التحقق من نوع السحب المطلوب
        if user_id in user_states:
            if user_states[user_id] == "waiting_for_single_post":
                # سحب منشور واحد
                await handle_single_post(client, message, channel_username, message_id)
            elif user_states[user_id] == "waiting_for_range_start":
                # سحب نطاق محدد - المنشور الأول
                await handle_range_start(client, message, channel_username, message_id, user_id)
            elif user_states[user_id] == "waiting_for_range_end":
                # سحب نطاق محدد - المنشور الأخير
                await handle_range_end(client, message, channel_username, message_id, user_id)
            elif user_states[user_id] == "waiting_for_forward_posts":
                # سحب تسلسلي للأمام
                await handle_forward_posts(client, message, channel_username, message_id)
            elif user_states[user_id] == "waiting_for_backward_posts":
                # سحب تسلسلي للخلف
                await handle_backward_posts(client, message, channel_username, message_id)
        else:
            # إذا لم تكن هناك حالة محددة، نفترض أنه سحب منشور واحد
            await handle_single_post(client, message, channel_username, message_id)

    except Exception as e:
        await message.reply_text(
            f"**❌ حدث خطأ غير متوقع**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]))
        logging.error(f"Unexpected error: {e}")


async def check_channel_accessibility(client, channel_username):
    """التحقق من إمكانية الوصول للقناة"""
    try:
        # محاولة الحصول على معلومات القناة
        chat = await client.get_chat(channel_username)

        # التحقق من نوع القناة
        if chat.type in ["private", "bot"]:
            return False, "القناة خاصة أو بوت"

        # للقنوات الخاصة (معرف رقمي)، التحقق من صلاحيات البوت
        if isinstance(channel_username, int):
            try:
                # التحقق من صلاحيات البوت في القناة
                bot_member = await client.get_chat_member(channel_username, "me")
                if bot_member.status in ["administrator", "creator"]:
                    # التحقق من صلاحية قراءة الرسائل
                    if hasattr(bot_member, 'privileges') and bot_member.privileges:
                        if bot_member.privileges.can_manage_chat or bot_member.privileges.can_delete_messages:
                            return True, "البوت له صلاحيات إدارية في القناة الخاصة"
                    return True, "البوت إدمن في القناة الخاصة"
                else:
                    return False, "البوت ليس إدمن في القناة الخاصة"
            except Exception as admin_check_error:
                return False, f"البوت غير مضاف كإدمن في القناة الخاصة: {str(admin_check_error)}"

        # للقنوات العامة
        return True, "القناة العامة متاحة"

    except Exception as e:
        error_msg = str(e).lower()
        if "chat_admin_required" in error_msg:
            return False, "القناة خاصة - يجب أن يكون البوت إدمن"
        elif "channel_private" in error_msg:
            return False, "القناة خاصة - البوت غير مضاف كإدمن"
        elif "peer_id_invalid" in error_msg:
            return False, "القناة غير موجودة أو الرابط خاطئ"
        elif "username_not_occupied" in error_msg:
            return False, "اسم المستخدم غير موجود"
        elif "username_invalid" in error_msg:
            return False, "اسم المستخدم غير صالح"
        else:
            return False, f"خطأ في الوصول للقناة: {str(e)}"


async def handle_single_post(client, message, channel_username, message_id):
    """معالجة سحب منشور واحد"""
    status_msg = await message.reply_text(
        "**⏳ جاري التحقق من القناة...**",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton("❌ إلغاء", callback_data="cancel_download")
        ]]))

    try:
        # التحقق من إمكانية الوصول للقناة أولاً
        accessible, access_msg = await check_channel_accessibility(client, channel_username)

        if not accessible:
            await status_msg.edit_text(
                f"**❌ لا يمكن الوصول للقناة**\n\n"
                f"**السبب:** {access_msg}\n\n"
                f"**💡 حلول مقترحة:**\n"
                f"**• للقنوات العامة:** تأكد من صحة الرابط\n"
                f"**• للقنوات الخاصة:** أضف البوت كإدمن مع صلاحية قراءة الرسائل\n"
                f"**• تأكد من أن القناة موجودة وغير محذوفة**\n\n"
                f"**📝 كيفية إضافة البوت كإدمن:**\n"
                f"**1. ادخل إعدادات القناة**\n"
                f"**2. اختر 'المشرفون'**\n"
                f"**3. أضف البوت وأعطه صلاحية 'حذف الرسائل' أو 'إدارة الدردشة'**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]))
            return

        # تحديث رسالة الحالة
        await status_msg.edit_text(
            "**⏳ جاري سحب المنشور...**",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ إلغاء", callback_data="cancel_download")
            ]]))

        msg = await client.get_messages(channel_username, message_id)
        if msg and not msg.empty:
            # إرسال للمستخدم في المجموعة الحالية فقط
            await msg.copy(message.chat.id)

            await status_msg.edit_text(
                "**✅ تم سحب المنشور بنجاح**\n",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف رسالة الحالة",
                                         callback_data="delete_now")
                ]]))
        else:
            await status_msg.edit_text(
                "**❌ المنشور غير موجود أو محذوف**\n\n"
                "**💡 تأكد من:**\n"
                "**• صحة رقم المنشور**\n"
                "**• أن المنشور لم يتم حذفه**\n"
                "**• أن الرابط صحيح**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]))
    except Exception as e:
        error_msg = str(e).lower()
        if "chat_admin_required" in error_msg:
            error_text = "**❌ القناة خاصة - تتطلب صلاحيات إدارية**"
        elif "channel_private" in error_msg:
            error_text = "**❌ القناة خاصة - غير متاحة للعامة**"
        elif "peer_id_invalid" in error_msg:
            error_text = "**❌ القناة غير موجودة أو الرابط خاطئ**"
        elif "message_id_invalid" in error_msg:
            error_text = "**❌ رقم المنشور غير صحيح أو المنشور محذوف**"
        else:
            error_text = f"**❌ حدث خطأ في السحب**\n`{str(e)}`"

        await status_msg.edit_text(
            f"{error_text}\n\n"
            f"**💡 ملاحظة:**\n"
            f"**• البوت يعمل فقط مع القنوات والمجموعات العامة**\n"
            f"**• لا يمكن سحب المحتوى من القنوات الخاصة**",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]))
        logging.error(f"Error downloading single post: {e}")


async def handle_all_posts(client, message, channel_username,
                           start_message_id):
    """معالجة سحب كل المنشورات"""
    stop_button = InlineKeyboardMarkup([[
        InlineKeyboardButton("⏹️ إيقاف السحب", callback_data="stop_download")
    ]])

    status_msg = await message.reply_text(
        "**⏳ جاري بدء عملية السحب التسلسلي...**", reply_markup=stop_button)

    try:
        user_id = message.from_user.id
        active_downloads[user_id] = True
        success_count = 0
        failed_count = 0
        consecutive_fails = 0
        current_id = start_message_id

        # التحقق من إمكانية الوصول للقناة أولاً
        accessible, access_msg = await check_channel_accessibility(client, channel_username)

        if not accessible:
            await status_msg.edit_text(
                f"**❌ لا يمكن الوصول للقناة**\n\n"
                f"**السبب:** {access_msg}\n\n"
                f"**💡 ملاحظة:**\n"
                f"**• البوت يعمل فقط مع القنوات والمجموعات العامة**\n"
                f"**• لا يمكن سحب المحتوى من القنوات الخاصة**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]))
            active_downloads.pop(user_id, None)
            user_states.pop(user_id, None)
            return

        while active_downloads.get(user_id, True) and consecutive_fails < 5:
            try:
                msg = await client.get_messages(channel_username, current_id)
                if msg and not msg.empty:
                    # إرسال للمستخدم في المجموعة فقط - محفوظ بشكل دائم
                    forwarded_msg = await msg.copy(message.chat.id)

                    if forwarded_msg:
                        success_count += 1
                        consecutive_fails = 0

                        # فترة راحة كل 50 منشور
                        if success_count % 95 == 0:
                            # التحقق من الاشتراك أثناء فترة الراحة
                            not_subscribed = await check_user_subscription(client, user_id)
                            if not_subscribed:
                                await status_msg.edit_text(
                                    "**❌ تم إيقاف العملية\n"
                                    "🔒 يجب الاشتراك في جميع القنوات للمتابعة\n"
                                    "📢 تحقق من اشتراكك وأعد المحاولة**")
                                active_downloads[user_id] = False
                                return

                            # عداد تنازلي لمدة دقيقتين (60 ثانية)
                            for remaining_time in range(60, 0, -1):
                                minutes = remaining_time // 60
                                seconds = remaining_time % 60
                                result = await safe_edit_message(
                                    status_msg,
                                    f"**⏸️ فترة راحة للحماية من الحظر\n"
                                    f"✅ تم سحب: {success_count} منشور\n"
                                    f"❌ فشل: {failed_count} منشور\n"
                                    f"⏰ الوقت المتبقي: {minutes:02d}:{seconds:02d}\n"
                                    f"🔄 سيتم استكمال العملية تلقائياً**",
                                    reply_markup=stop_button)

                                if result is None:  # فشل التحديث
                                    break

                                await asyncio.sleep(1)

                                # التحقق من إيقاف العملية أثناء الانتظار
                                if not active_downloads.get(user_id, True):
                                    return

                        await safe_edit_message(
                            status_msg,
                            f"**⏳ جاري السحب التسلسلي...\n"
                            f"✅ تم سحب: {success_count} منشور\n"
                            f"❌ فشل: {failed_count} منشور\n"
                            f"🔄 جاري معالجة المنشور رقم: {current_id}**",
                            reply_markup=stop_button)
                else:
                    failed_count += 1
                    consecutive_fails += 1
            except Exception as e:
                failed_count += 1
                consecutive_fails += 1
                logging.error(f"Error processing message {current_id}: {e}")

            current_id += 1
            await asyncio.sleep(3)  # 3 ثوان للحماية القصوى من الحظر

        await status_msg.edit_text(
            f"**✅ اكتملت عملية السحب\n"
            f"✅ تم سحب: {success_count} منشور\n"
            f"❌ فشل: {failed_count} منشور\n"
            f"📌 جميع المنشورات محفوظة بشكل دائم**",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف رسالة الحالة",
                                     callback_data="delete_now")
            ]]))

    except Exception as e:
        await status_msg.edit_text(
            f"**❌ حدث خطأ في عملية السحب**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]))
        logging.error(f"Sequential download error: {e}")

    finally:
        active_downloads.pop(user_id, None)
        user_states.pop(user_id, None)


@app.on_callback_query(filters.regex("^cancel_download$"))
async def cancel_download_button(_: Client, callback: CallbackQuery):
    try:
        user_id = callback.from_user.id
        if user_id in active_downloads:
            active_downloads[user_id] = False
            await callback.message.edit_text("**تم إلغاء عملية السحب ❌**")
        await callback.answer()
    except Exception as e:
        logging.error(f"Error in cancel download button: {e}")
        await callback.answer("حدث خطأ! ❌")


async def handle_range_start(_, message, channel_username, message_id, user_id):
    """معالجة بداية النطاق المحدد"""
    # حفظ بيانات البداية
    range_data[user_id] = {
        'channel': channel_username,
        'start_id': message_id
    }

    # طلب المنشور الأخير
    user_states[user_id] = "waiting_for_range_end"
    await message.reply_text(
        f"**تم حفظ المنشور الأول: {message_id}**\n\n"
        "**الآن قم بإرسال رابط آخر منشور تريد الانتهاء عنده:**\n"
        "**مثال: https://t.me/channel_name/200**",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")
        ]]))


async def handle_range_end(client, message, channel_username, message_id, user_id):
    """معالجة نهاية النطاق المحدد"""
    if user_id not in range_data:
        await message.reply_text("**❌ خطأ: لم يتم العثور على بيانات البداية**")
        return

    start_id = range_data[user_id]['start_id']
    start_channel = range_data[user_id]['channel']

    # التأكد من أن القناة نفسها
    if channel_username != start_channel:
        await message.reply_text("**❌ خطأ: يجب أن يكون المنشوران من نفس القناة**")
        return

    # تحديد الاتجاه
    if start_id <= message_id:
        # سحب من الأقدم إلى الأحدث
        await handle_range_download(client, message, channel_username, start_id, message_id, "forward")
    else:
        # سحب من الأحدث إلى الأقدم
        await handle_range_download(client, message, channel_username, message_id, start_id, "backward")

    # تنظيف البيانات
    range_data.pop(user_id, None)
    user_states.pop(user_id, None)


async def handle_forward_posts(client, message, channel_username, start_message_id):
    """معالجة السحب التسلسلي للأمام"""
    await handle_sequential_download(client, message, channel_username, start_message_id, "forward")


async def handle_backward_posts(client, message, channel_username, start_message_id):
    """معالجة السحب التسلسلي للخلف"""
    await handle_sequential_download(client, message, channel_username, start_message_id, "backward")


async def handle_range_download(client, message, channel_username, start_id, end_id, direction):
    """معالجة سحب نطاق محدد من المنشورات"""
    stop_button = InlineKeyboardMarkup([[
        InlineKeyboardButton("⏹️ إيقاف السحب", callback_data="stop_download")
    ]])

    status_msg = await message.reply_text(
        f"**⏳ جاري بدء سحب النطاق المحدد...\n"
        f"📊 من المنشور {start_id} إلى {end_id}**",
        reply_markup=stop_button)

    try:
        user_id = message.from_user.id
        active_downloads[user_id] = True
        success_count = 0
        failed_count = 0

        # التحقق من إمكانية الوصول للقناة أولاً
        accessible, access_msg = await check_channel_accessibility(client, channel_username)

        if not accessible:
            await status_msg.edit_text(
                f"**❌ لا يمكن الوصول للقناة**\n\n"
                f"**السبب:** {access_msg}\n\n"
                f"**💡 ملاحظة:**\n"
                f"**• البوت يعمل فقط مع القنوات والمجموعات العامة**\n"
                f"**• لا يمكن سحب المحتوى من القنوات الخاصة**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]))
            active_downloads.pop(user_id, None)
            return

        # تحديد النطاق والاتجاه
        if direction == "forward":
            current_range = range(start_id, end_id + 1)
        else:
            current_range = range(start_id, end_id - 1, -1)

        total_posts = abs(end_id - start_id) + 1
        processed = 0

        for current_id in current_range:
            if not active_downloads.get(user_id, True):
                break

            try:
                msg = await client.get_messages(channel_username, current_id)
                if msg and not msg.empty:
                    # إرسال المنشور - محفوظ بشكل دائم
                    forwarded_msg = await msg.copy(message.chat.id)
                    if forwarded_msg:
                        success_count += 1

                        # فترة راحة كل 50 منشور
                        if success_count % 95 == 0:
                            # التحقق من الاشتراك أثناء فترة الراحة
                            not_subscribed = await check_user_subscription(client, user_id)
                            if not_subscribed:
                                await status_msg.edit_text(
                                    "**❌ تم إيقاف العملية\n"
                                    "🔒 يجب الاشتراك في جميع القنوات للمتابعة\n"
                                    "📢 تحقق من اشتراكك وأعد المحاولة**")
                                active_downloads[user_id] = False
                                return

                            # عداد تنازلي لمدة دقيقتين (60 ثانية)
                            for remaining_time in range(60, 0, -1):
                                minutes = remaining_time // 60
                                seconds = remaining_time % 60
                                await status_msg.edit_text(
                                    f"**⏸️ فترة راحة للحماية من الحظر\n"
                                    f"📊 النطاق: من {start_id} إلى {end_id}\n"
                                    f"✅ تم سحب: {success_count} منشور\n"
                                    f"❌ فشل: {failed_count} منشور\n"
                                    f"⏰ الوقت المتبقي: {minutes:02d}:{seconds:02d}\n"
                                    f"🔄 سيتم استكمال العملية تلقائياً**",
                                    reply_markup=stop_button)
                                await asyncio.sleep(1)

                                # التحقق من إيقاف العملية أثناء الانتظار
                                if not active_downloads.get(user_id, True):
                                    return
                else:
                    failed_count += 1
            except Exception as e:
                failed_count += 1
                logging.error(f"Error processing message {current_id}: {e}")

            processed += 1
            progress = (processed / total_posts) * 100

            await status_msg.edit_text(
                f"**⏳ جاري سحب النطاق المحدد...\n"
                f"📊 من المنشور {start_id} إلى {end_id}\n"
                f"✅ تم سحب: {success_count} منشور\n"
                f"❌ فشل: {failed_count} منشور\n"
                f"📈 التقدم: {progress:.1f}%\n"
                f"🔄 جاري معالجة المنشور رقم: {current_id}**",
                reply_markup=stop_button)

            await asyncio.sleep(3)  # 3 ثوان للحماية القصوى من الحظر

        await status_msg.edit_text(
            f"**✅ اكتملت عملية سحب النطاق\n"
            f"📊 النطاق: من {start_id} إلى {end_id}\n"
            f"✅ تم سحب: {success_count} منشور\n"
            f"❌ فشل: {failed_count} منشور\n"
            f"📌 جميع المنشورات محفوظة بشكل دائم**",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف رسالة الحالة",
                                     callback_data="delete_now")
            ]]))

    except Exception as e:
        await status_msg.edit_text(
            f"**❌ حدث خطأ في سحب النطاق**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]))
        logging.error(f"Range download error: {e}")

    finally:
        active_downloads.pop(user_id, None)


async def handle_sequential_download(client, message, channel_username, start_message_id, direction):
    """معالجة السحب التسلسلي (للأمام أو للخلف)"""
    stop_button = InlineKeyboardMarkup([[
        InlineKeyboardButton("⏹️ إيقاف السحب", callback_data="stop_download")
    ]])

    direction_text = "للأمام ⬆️" if direction == "forward" else "للخلف ⬇️"
    status_msg = await message.reply_text(
        f"**⏳ جاري بدء السحب التسلسلي {direction_text}...**",
        reply_markup=stop_button)

    try:
        user_id = message.from_user.id
        active_downloads[user_id] = True
        success_count = 0
        failed_count = 0
        consecutive_fails = 0
        current_id = start_message_id

        # التحقق من إمكانية الوصول للقناة أولاً
        accessible, access_msg = await check_channel_accessibility(client, channel_username)

        if not accessible:
            await status_msg.edit_text(
                f"**❌ لا يمكن الوصول للقناة**\n\n"
                f"**السبب:** {access_msg}\n\n"
                f"**💡 ملاحظة:**\n"
                f"**• البوت يعمل فقط مع القنوات والمجموعات العامة**\n"
                f"**• لا يمكن سحب المحتوى من القنوات الخاصة**",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]))
            active_downloads.pop(user_id, None)
            user_states.pop(user_id, None)
            return

        while active_downloads.get(user_id, True) and consecutive_fails < 5:
            try:
                msg = await client.get_messages(channel_username, current_id)
                if msg and not msg.empty:
                    # إرسال المنشور - محفوظ بشكل دائم
                    forwarded_msg = await msg.copy(message.chat.id)
                    if forwarded_msg:
                        success_count += 1
                        consecutive_fails = 0

                        # فترة راحة كل 50 منشور
                        if success_count % 95 == 0:
                            # التحقق من الاشتراك أثناء فترة الراحة
                            not_subscribed = await check_user_subscription(client, user_id)
                            if not_subscribed:
                                await status_msg.edit_text(
                                    "**❌ تم إيقاف العملية\n"
                                    "🔒 يجب الاشتراك في جميع القنوات للمتابعة\n"
                                    "📢 تحقق من اشتراكك وأعد المحاولة**")
                                active_downloads[user_id] = False
                                return

                            # عداد تنازلي لمدة دقيقتين (60 ثانية)
                            for remaining_time in range(60, 0, -1):
                                minutes = remaining_time // 60
                                seconds = remaining_time % 60
                                await status_msg.edit_text(
                                    f"**⏸️ فترة راحة للحماية من الحظر\n"
                                    f"✅ تم سحب: {success_count} منشور\n"
                                    f"❌ فشل: {failed_count} منشور\n"
                                    f"⏰ الوقت المتبقي: {minutes:02d}:{seconds:02d}\n"
                                    f"🔄 سيتم استكمال العملية تلقائياً**",
                                    reply_markup=stop_button)
                                await asyncio.sleep(1)

                                # التحقق من إيقاف العملية أثناء الانتظار
                                if not active_downloads.get(user_id, True):
                                    return

                        await status_msg.edit_text(
                            f"**⏳ جاري السحب التسلسلي {direction_text}...\n"
                            f"✅ تم سحب: {success_count} منشور\n"
                            f"❌ فشل: {failed_count} منشور\n"
                            f"🔄 جاري معالجة المنشور رقم: {current_id}**",
                            reply_markup=stop_button)
                else:
                    failed_count += 1
                    consecutive_fails += 1
            except Exception as e:
                failed_count += 1
                consecutive_fails += 1
                logging.error(f"Error processing message {current_id}: {e}")

            # تحديد الاتجاه
            if direction == "forward":
                current_id += 1
            else:
                current_id -= 1

            await asyncio.sleep(3)  # 3 ثوان للحماية القصوى من الحظر

        await status_msg.edit_text(
            f"**✅ اكتملت عملية السحب التسلسلي {direction_text}\n"
            f"✅ تم سحب: {success_count} منشور\n"
            f"❌ فشل: {failed_count} منشور\n"
            f"📌 جميع المنشورات محفوظة بشكل دائم**",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف رسالة الحالة",
                                     callback_data="delete_now")
            ]]))

    except Exception as e:
        await status_msg.edit_text(
            f"**❌ حدث خطأ في السحب التسلسلي**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]))
        logging.error(f"Sequential download error: {e}")

    finally:
        active_downloads.pop(user_id, None)
        user_states.pop(user_id, None)


async def main():
    try:
        await app.start()
        print("✅ Bot started successfully!")
        print("🔄 Bot is now running and ready to receive messages...")
        print("📱 You can now test the bot with Telegram links")
        # استخدام حلقة لا نهائية للحفاظ على البوت قيد التشغيل
        while True:
            await asyncio.sleep(1)
    except Exception as e:
        print(f"❌ Error starting bot: {e}")
        raise


if __name__ == "__main__":
    try:
        app.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user!")
        app.stop()
    except Exception as e:
        logger.error(f"Critical error: {e}")




